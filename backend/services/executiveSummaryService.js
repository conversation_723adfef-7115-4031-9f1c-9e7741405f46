// Executive Summary Service for Borouge ESG Intelligence Platform
// Generates comprehensive, strategic executive summaries for C-level decision making

class ExecutiveSummaryService {
  constructor(config, supabase, aiService) {
    this.config = config;
    this.supabase = supabase;
    this.aiService = aiService;
  }

  // Generate comprehensive executive summary from analyzed articles
  async generateComprehensiveExecutiveSummary(query, analyzedArticles, queryEnhancements) {
    try {
      console.log(`📊 Generating comprehensive executive summary for ${analyzedArticles.length} articles`);

      // Categorize articles by impact and relevance
      const categorizedArticles = this.categorizeArticles(analyzedArticles);
      
      // Generate quantitative insights
      const quantitativeInsights = this.generateQuantitativeInsights(analyzedArticles);
      
      // Create strategic analysis prompt
      const summaryPrompt = this.createExecutiveSummaryPrompt(
        query, 
        categorizedArticles, 
        quantitativeInsights, 
        queryEnhancements
      );

      // Generate AI-powered executive summary
      const aiSummary = await this.aiService.analyzeQuery(summaryPrompt);
      
      // Parse and structure the summary
      const structuredSummary = this.parseExecutiveSummary(aiSummary.response);
      
      // Add quantitative data
      structuredSummary.quantitative_insights = quantitativeInsights;
      structuredSummary.article_breakdown = categorizedArticles;
      
      console.log(`✅ Executive summary generated successfully`);
      return structuredSummary;

    } catch (error) {
      console.error('❌ Error generating executive summary:', error);
      return this.generateFallbackExecutiveSummary(query, analyzedArticles);
    }
  }

  // Categorize articles by impact level and business relevance
  categorizeArticles(articles) {
    const categories = {
      critical: articles.filter(a => a.impact_level === 'CRITICAL'),
      high_impact: articles.filter(a => a.impact_level === 'HIGH'),
      opportunities: articles.filter(a => a.impact_level === 'OPPORTUNITY'),
      medium_impact: articles.filter(a => a.impact_level === 'MEDIUM'),
      monitoring: articles.filter(a => a.impact_level === 'LOW'),
      highly_relevant: articles.filter(a => (a.relevance_score || 0) >= 80),
      regulatory: articles.filter(a => this.isRegulatoryContent(a)),
      competitive: articles.filter(a => this.isCompetitiveContent(a)),
      esg_focused: articles.filter(a => this.isESGContent(a))
    };

    return {
      ...categories,
      total_articles: articles.length,
      actionable_articles: categories.critical.length + categories.high_impact.length + categories.opportunities.length
    };
  }

  // Generate quantitative insights from articles
  generateQuantitativeInsights(articles) {
    const totalArticles = articles.length;
    const relevanceScores = articles.map(a => a.relevance_score || 0);
    const avgRelevance = relevanceScores.reduce((sum, score) => sum + score, 0) / totalArticles;

    // Calculate impact distribution
    const impactDistribution = {
      critical: articles.filter(a => a.impact_level === 'CRITICAL').length,
      high: articles.filter(a => a.impact_level === 'HIGH').length,
      medium: articles.filter(a => a.impact_level === 'MEDIUM').length,
      low: articles.filter(a => a.impact_level === 'LOW').length,
      opportunity: articles.filter(a => a.impact_level === 'OPPORTUNITY').length
    };

    // Calculate timeline urgency
    const urgentArticles = articles.filter(a => 
      a.risk_assessment?.timeline?.includes('immediate') || 
      a.risk_assessment?.timeline?.includes('3-6 months')
    ).length;

    // Calculate geographic relevance
    const geographicRelevance = this.calculateGeographicRelevance(articles);

    return {
      total_articles: totalArticles,
      average_relevance: Math.round(avgRelevance),
      impact_distribution: impactDistribution,
      urgent_attention_required: urgentArticles,
      actionable_insights: impactDistribution.critical + impactDistribution.high + impactDistribution.opportunity,
      geographic_relevance: geographicRelevance,
      confidence_level: this.calculateOverallConfidence(articles)
    };
  }

  // Create comprehensive executive summary prompt
  createExecutiveSummaryPrompt(query, categorizedArticles, quantitativeInsights, queryEnhancements) {
    return `As Borouge's Chief Executive Officer, provide a comprehensive executive summary for board-level decision making:

SEARCH CONTEXT:
Query: ${query}
Enhanced Keywords: ${queryEnhancements.enhancedKeywords?.join(', ') || 'None'}
Analysis Date: ${new Date().toISOString().split('T')[0]}

QUANTITATIVE OVERVIEW:
- Total Articles Analyzed: ${quantitativeInsights.total_articles}
- Average Relevance Score: ${quantitativeInsights.average_relevance}%
- Critical Issues: ${quantitativeInsights.impact_distribution.critical}
- High Impact Items: ${quantitativeInsights.impact_distribution.high}
- Strategic Opportunities: ${quantitativeInsights.impact_distribution.opportunity}
- Urgent Attention Required: ${quantitativeInsights.urgent_attention_required} items

ARTICLE BREAKDOWN:
- Critical Issues: ${categorizedArticles.critical.length} articles
- High Impact: ${categorizedArticles.high_impact.length} articles
- Opportunities: ${categorizedArticles.opportunities.length} articles
- Regulatory Focus: ${categorizedArticles.regulatory.length} articles
- Competitive Intelligence: ${categorizedArticles.competitive.length} articles
- ESG/Sustainability: ${categorizedArticles.esg_focused.length} articles

BOROUGE STRATEGIC CONTEXT:
- Revenue: $8.5B annually (€2.3B EU exposure)
- Operations: Ruwais Complex (5.0M tonnes), Singapore hub
- Key Markets: UAE, Singapore, China, India, Europe
- ESG Goals: Carbon neutrality by 2050, circular economy leadership
- Competitive Position: #3 Middle East petrochemicals vs SABIC, Dow

Provide a comprehensive JSON executive summary with this structure:

{
  "executive_overview": {
    "key_findings": "3-4 sentence summary of most critical findings for CEO attention",
    "strategic_implications": "How these findings impact Borouge's strategy and competitive position",
    "urgency_assessment": "Timeline for required actions (immediate, 3-6 months, 6-12 months)",
    "confidence_level": "High/Medium/Low confidence in analysis and recommendations"
  },
  "business_impact_analysis": {
    "revenue_implications": "Potential impact on $8.5B revenue (quantified where possible)",
    "operational_impact": "Effects on Ruwais operations and Singapore hub",
    "market_positioning": "Impact on competitive position vs SABIC, Dow, ExxonMobil",
    "regulatory_compliance": "Compliance requirements and associated costs/risks"
  },
  "strategic_priorities": {
    "immediate_actions": [
      {
        "priority": "Action requiring immediate CEO/board attention",
        "business_rationale": "Why this is critical for Borouge",
        "resource_requirement": "Investment/resources needed",
        "success_metrics": "How to measure success",
        "timeline": "Implementation timeframe"
      }
    ],
    "medium_term_initiatives": [
      {
        "initiative": "Strategic initiative for 6-18 month horizon",
        "strategic_value": "Long-term value creation potential",
        "investment_required": "Estimated investment range",
        "expected_roi": "Expected return or strategic benefit"
      }
    ]
  },
  "risk_and_opportunity_matrix": {
    "critical_risks": [
      {
        "risk": "Specific risk to Borouge operations",
        "probability": "High/Medium/Low",
        "impact": "Financial and operational impact",
        "mitigation": "Recommended mitigation strategy"
      }
    ],
    "strategic_opportunities": [
      {
        "opportunity": "Specific market or competitive opportunity",
        "market_size": "Addressable market size if known",
        "competitive_advantage": "How Borouge can win",
        "investment_thesis": "Why this is attractive for Borouge"
      }
    ]
  },
  "esg_and_sustainability": {
    "carbon_neutrality_impact": "Relevance to 2050 carbon neutrality goal",
    "circular_economy_opportunities": "Circular economy and recycling implications",
    "regulatory_landscape": "ESG regulatory changes affecting operations",
    "stakeholder_expectations": "Impact on investor and customer expectations"
  },
  "competitive_intelligence": {
    "competitor_movements": "Key competitor actions and implications",
    "market_dynamics": "Changing market conditions in key regions",
    "technology_trends": "Relevant technology or innovation developments",
    "supply_chain_insights": "Supply chain or trade policy implications"
  },
  "financial_implications": {
    "cost_impact": "Estimated cost implications (ranges where possible)",
    "revenue_opportunities": "Revenue upside potential",
    "capex_requirements": "Capital expenditure needs",
    "operational_efficiency": "Operational cost savings or increases"
  },
  "recommended_actions": {
    "board_decisions_required": ["Decision 1", "Decision 2"],
    "management_actions": ["Action 1", "Action 2"],
    "stakeholder_communications": "Key messages for investors, customers, regulators",
    "next_steps": "Immediate next steps for executive team"
  }
}

ANALYSIS GUIDELINES:
- Focus on strategic, board-level insights
- Quantify financial impacts where possible
- Consider Borouge's specific market position and capabilities
- Emphasize actionable recommendations
- Include both defensive (risk) and offensive (opportunity) strategies
- Reference industry benchmarks and competitive dynamics
- Consider ESG and sustainability implications
- Provide clear timelines and resource requirements`;
  }

  // Parse AI-generated executive summary
  parseExecutiveSummary(aiResponse) {
    try {
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const summary = JSON.parse(jsonMatch[0]);
        return this.validateAndStructureSummary(summary);
      }
    } catch (error) {
      console.error('❌ Error parsing executive summary:', error);
    }
    
    return this.generateFallbackExecutiveSummary();
  }

  // Validate and structure the executive summary
  validateAndStructureSummary(summary) {
    return {
      executive_overview: summary.executive_overview || {
        key_findings: 'Analysis in progress',
        strategic_implications: 'Assessment pending',
        urgency_assessment: 'Timeline evaluation needed',
        confidence_level: 'medium'
      },
      business_impact_analysis: summary.business_impact_analysis || {},
      strategic_priorities: summary.strategic_priorities || { immediate_actions: [], medium_term_initiatives: [] },
      risk_and_opportunity_matrix: summary.risk_and_opportunity_matrix || { critical_risks: [], strategic_opportunities: [] },
      esg_and_sustainability: summary.esg_and_sustainability || {},
      competitive_intelligence: summary.competitive_intelligence || {},
      financial_implications: summary.financial_implications || {},
      recommended_actions: summary.recommended_actions || {}
    };
  }

  // Helper methods for content classification
  isRegulatoryContent(article) {
    const content = `${article.title} ${article.description || ''}`.toLowerCase();
    const regulatoryKeywords = ['regulation', 'compliance', 'law', 'policy', 'ban', 'restriction', 'mandate', 'requirement'];
    return regulatoryKeywords.some(keyword => content.includes(keyword));
  }

  isCompetitiveContent(article) {
    const content = `${article.title} ${article.description || ''}`.toLowerCase();
    const competitorKeywords = ['sabic', 'dow', 'exxonmobil', 'basf', 'lyondellbasell', 'sinopec', 'petrochina'];
    return competitorKeywords.some(keyword => content.includes(keyword));
  }

  isESGContent(article) {
    const content = `${article.title} ${article.description || ''}`.toLowerCase();
    const esgKeywords = ['esg', 'sustainability', 'carbon', 'environment', 'circular', 'recycling', 'green'];
    return esgKeywords.some(keyword => content.includes(keyword));
  }

  // Calculate geographic relevance
  calculateGeographicRelevance(articles) {
    const regions = ['uae', 'singapore', 'china', 'india', 'europe', 'asia'];
    const relevance = {};
    
    regions.forEach(region => {
      relevance[region] = articles.filter(article => {
        const content = `${article.title} ${article.description || ''}`.toLowerCase();
        return content.includes(region);
      }).length;
    });
    
    return relevance;
  }

  // Calculate overall confidence level
  calculateOverallConfidence(articles) {
    const confidenceLevels = articles.map(a => a.quantitative_insights?.confidence_level || 'medium');
    const highConfidence = confidenceLevels.filter(c => c === 'high').length;
    const totalArticles = articles.length;
    
    if (highConfidence / totalArticles > 0.7) return 'high';
    if (highConfidence / totalArticles > 0.4) return 'medium';
    return 'low';
  }

  // Generate fallback executive summary
  generateFallbackExecutiveSummary(query = '', articles = []) {
    return {
      executive_overview: {
        key_findings: `Analysis of ${articles.length} articles related to "${query}" requires manual executive review`,
        strategic_implications: 'Strategic assessment pending comprehensive analysis',
        urgency_assessment: 'Timeline evaluation needed',
        confidence_level: 'low'
      },
      business_impact_analysis: {
        revenue_implications: 'Revenue impact assessment required',
        operational_impact: 'Operational analysis needed',
        market_positioning: 'Competitive positioning review required',
        regulatory_compliance: 'Compliance assessment pending'
      },
      strategic_priorities: {
        immediate_actions: [{
          priority: 'Conduct comprehensive manual analysis',
          business_rationale: 'Ensure accurate strategic assessment',
          resource_requirement: 'Senior analyst team',
          success_metrics: 'Completed strategic analysis',
          timeline: '1-2 weeks'
        }],
        medium_term_initiatives: []
      },
      risk_and_opportunity_matrix: {
        critical_risks: [],
        strategic_opportunities: []
      },
      esg_and_sustainability: {
        carbon_neutrality_impact: 'ESG impact assessment required',
        circular_economy_opportunities: 'Circular economy analysis needed',
        regulatory_landscape: 'Regulatory review pending',
        stakeholder_expectations: 'Stakeholder impact assessment required'
      },
      competitive_intelligence: {
        competitor_movements: 'Competitive analysis required',
        market_dynamics: 'Market assessment needed',
        technology_trends: 'Technology review pending',
        supply_chain_insights: 'Supply chain analysis required'
      },
      financial_implications: {
        cost_impact: 'Cost analysis pending',
        revenue_opportunities: 'Revenue assessment required',
        capex_requirements: 'Capital requirements evaluation needed',
        operational_efficiency: 'Efficiency analysis pending'
      },
      recommended_actions: {
        board_decisions_required: ['Approve comprehensive analysis budget'],
        management_actions: ['Initiate detailed strategic review'],
        stakeholder_communications: 'Communication strategy development needed',
        next_steps: 'Schedule executive strategy session'
      }
    };
  }
}

module.exports = ExecutiveSummaryService;
